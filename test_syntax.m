% 测试MATLAB代码语法
function test_syntax()
    try
        % 检查函数语法
        fprintf('正在检查 hybrid_vehicle_simulink_model.m 的语法...\n');
        
        % 使用mlint检查语法（如果可用）
        if exist('mlint', 'file')
            result = mlint('hybrid_vehicle_simulink_model.m', '-string');
            if isempty(result)
                fprintf('✓ 语法检查通过，没有发现错误\n');
            else
                fprintf('发现以下问题:\n');
                disp(result);
            end
        else
            fprintf('mlint不可用，跳过语法检查\n');
        end
        
        % 检查函数是否可以被解析
        try
            which('hybrid_vehicle_simulink_model')
            fprintf('✓ 函数文件可以被MATLAB识别\n');
        catch ME
            fprintf('✗ 函数文件识别失败: %s\n', ME.message);
        end
        
    catch ME
        fprintf('测试过程中出现错误: %s\n', ME.message);
    end
end
