function hybrid_vehicle_simulink_model()
% 创建混合动力汽车控制系统 Simulink 模型
% 完全复制图片中显示的系统架构

%% 清理工作空间并创建新模型
close_system(find_system('Type', 'block_diagram'), 0);
clc;

% 创建新的 Simulink 模型
model_name = 'HybridVehicleControlSystem';
new_system(model_name);
open_system(model_name);

%% 定义模块位置坐标 (基于图片布局)
% 坐标格式: [left top right bottom]

% 上层动力系统组件
engine_pos = [50 50 150 100];           % Engine
battery_pos = [50 180 150 230];         % Battery  
dcdc_pos = [200 150 300 200];           % DC-DC Converter
motor_pos = [350 120 450 170];          % Motor
gearbox_pos = [350 50 450 100];         % Gearbox
vehicle_pos = [550 100 650 170];        % Vehicle Body

% 显示和监测组件
scopes_pos = [750 50 850 100];          % Scopes
speed_display_pos = [750 120 850 170];   % Speed Display

% 下层控制系统组件  
speed_cmd_pos = [50 300 150 350];       % Speed Command
strategy_pos = [300 280 450 350];       % Strategy Routing
fx_zero_pos = [50 250 150 280];         % f(x)=0

% 参考速度
ref_spd_pos = [750 200 850 230];        % ref_spd

%% 添加所有模块

% {{ AURA-X: Create - 添加动力系统核心模块 }}
% 发动机模块
add_block('built-in/TransferFcn', [model_name '/Engine'], ...
    'Position', engine_pos, ...
    'Numerator', '[1]', ...
    'Denominator', '[0.1 1]', ...
    'BackgroundColor', 'lightBlue');

% 电池模块
add_block('built-in/Constant', [model_name '/Battery'], ...
    'Position', battery_pos, ...
    'Value', '400', ...
    'BackgroundColor', 'yellow');

% DC-DC转换器
add_block('built-in/Gain', [model_name '/DC-DC Converter'], ...
    'Position', dcdc_pos, ...
    'Gain', '1.5');

% 电机模块 - 使用Product块来接收电压和控制信号
add_block('built-in/Product', [model_name '/Motor'], ...
    'Position', motor_pos, ...
    'Inputs', '2', ...
    'BackgroundColor', 'cyan');

% 电机传递函数
add_block('built-in/TransferFcn', [model_name '/Motor_TF'], ...
    'Position', [motor_pos(1)+100 motor_pos(2) motor_pos(3)+100 motor_pos(4)], ...
    'Numerator', '[100]', ...
    'Denominator', '[0.05 1]', ...
    'BackgroundColor', 'cyan');

% 变速箱模块
add_block('built-in/Gain', [model_name '/Gearbox'], ...
    'Position', gearbox_pos, ...
    'Gain', '3.5');

% 车辆本体模块
add_block('built-in/TransferFcn', [model_name '/Vehicle Body'], ...
    'Position', vehicle_pos, ...
    'Numerator', '[1]', ...
    'Denominator', '[1000 50 1]', ...
    'BackgroundColor', 'white');

%% {{ AURA-X: Create - 添加控制和策略模块 }}
% 速度指令
add_block('built-in/Step', [model_name '/Speed Command'], ...
    'Position', speed_cmd_pos, ...
    'Time', '1', ...
    'Before', '0', ...
    'After', '60', ...
    'BackgroundColor', 'orange');

% 策略路由控制器
add_block('built-in/SubSystem', [model_name '/Strategy Routing'], ...
    'Position', strategy_pos);

% f(x)=0 函数块
add_block('built-in/Constant', [model_name '/f(x)=0'], ...
    'Position', fx_zero_pos, ...
    'Value', '0', ...
    'BackgroundColor', 'white');

%% {{ AURA-X: Create - 添加显示和监测模块 }}
% 示波器
add_block('built-in/Scope', [model_name '/Scopes'], ...
    'Position', scopes_pos);

% 速度显示
add_block('built-in/Display', [model_name '/Speed'], ...
    'Position', speed_display_pos);

% 参考速度
add_block('built-in/Display', [model_name '/ref_spd'], ...
    'Position', ref_spd_pos);

%% {{ AURA-X: Create - 配置策略路由子系统 }}
% 打开策略路由子系统进行配置
strategy_subsystem = [model_name '/Strategy Routing'];
open_system(strategy_subsystem);

% 在子系统中添加输入输出端口
add_block('built-in/Inport', [strategy_subsystem '/Spd'], ...
    'Position', [50 50 100 80], ...
    'Port', '1');

% 第一个输出端口 (电机控制信号M)
add_block('built-in/Outport', [strategy_subsystem '/M'], ...
    'Position', [300 50 350 80], ...
    'Port', '1');

% 第二个输出端口 (发动机控制信号E)
add_block('built-in/Outport', [strategy_subsystem '/E'], ...
    'Position', [300 120 350 150], ...
    'Port', '2');

% 添加控制逻辑 - 使用Gain块作为简单的控制策略
add_block('built-in/Gain', [strategy_subsystem '/Motor Control'], ...
    'Position', [150 50 200 80], ...
    'Gain', '0.7');
add_block('built-in/Gain', [strategy_subsystem '/Engine Control'], ...
    'Position', [150 120 200 150], ...
    'Gain', '0.3');

% 连接子系统内部
add_line(strategy_subsystem, 'Spd/1', 'Motor Control/1');
add_line(strategy_subsystem, 'Spd/1', 'Engine Control/1');
add_line(strategy_subsystem, 'Motor Control/1', 'M/1');
add_line(strategy_subsystem, 'Engine Control/1', 'E/1');

% 关闭子系统
close_system(strategy_subsystem);

%% {{ AURA-X: Create - 添加所有信号连接线 }}
% {{ AURA-X: Modify - 修正连接逻辑，确保所有模块正确连接 }}

% 控制系统连接 - 首先建立控制信号
add_line(model_name, 'Speed Command/1', 'Strategy Routing/1', 'autorouting', 'on');

% 策略控制器到发动机和电机的控制信号
add_line(model_name, 'Strategy Routing/2', 'Engine/1', 'autorouting', 'on');

% 发动机动力传递路径：Engine -> Gearbox -> Vehicle Body
add_line(model_name, 'Engine/1', 'Gearbox/1', 'autorouting', 'on');

% 电力系统连接：Battery -> DC-DC Converter -> Motor
add_line(model_name, 'Battery/1', 'DC-DC Converter/1', 'autorouting', 'on');
add_line(model_name, 'DC-DC Converter/1', 'Motor/1', 'autorouting', 'on');

% 电机控制信号连接
add_line(model_name, 'Strategy Routing/1', 'Motor/2', 'autorouting', 'on');

% 电机输出连接到传递函数
add_line(model_name, 'Motor/1', 'Motor_TF/1', 'autorouting', 'on');

% 动力汇聚到车辆本体 - 使用Sum模块来合并F和R信号
add_block('built-in/Sum', [model_name '/Power_Sum'], ...
    'Position', [500 135 530 165], ...
    'Inputs', '++');

% 连接变速箱和电机输出到求和模块
add_line(model_name, 'Gearbox/1', 'Power_Sum/1', 'autorouting', 'on');
add_line(model_name, 'Motor_TF/1', 'Power_Sum/2', 'autorouting', 'on');

% 求和模块输出到车辆本体
add_line(model_name, 'Power_Sum/1', 'Vehicle Body/1', 'autorouting', 'on');

% 输出显示连接
add_line(model_name, 'Vehicle Body/1', 'Speed/1', 'autorouting', 'on');
add_line(model_name, 'Vehicle Body/1', 'Scopes/1', 'autorouting', 'on');
add_line(model_name, 'Speed Command/1', 'ref_spd/1', 'autorouting', 'on');

% 电池状态监测连接 - f(x)=0是常数块，不需要输入连接
% add_line(model_name, 'Battery/1', 'f(x)=0/1', 'autorouting', 'on'); % 已注释，常数块不需要输入

%% {{ AURA-X: Create - 添加带标签的信号线 }}
% 创建带有正确标签的信号线，覆盖之前的连接

% 安全地删除和重新创建带标签的信号线
try
    % DF信号：Engine到Gearbox
    try
        delete_line(model_name, 'Engine/1', 'Gearbox/1');
    catch
        % 连接可能不存在，继续
    end
    h1 = add_line(model_name, 'Engine/1', 'Gearbox/1', 'autorouting', 'on');
    set_param(h1, 'Name', 'DF');

    % F信号：Gearbox到Power_Sum
    try
        delete_line(model_name, 'Gearbox/1', 'Power_Sum/1');
    catch
        % 连接可能不存在，继续
    end
    h2 = add_line(model_name, 'Gearbox/1', 'Power_Sum/1', 'autorouting', 'on');
    set_param(h2, 'Name', 'F');

    % V信号：Battery到DC-DC Converter
    try
        delete_line(model_name, 'Battery/1', 'DC-DC Converter/1');
    catch
        % 连接可能不存在，继续
    end
    h3 = add_line(model_name, 'Battery/1', 'DC-DC Converter/1', 'autorouting', 'on');
    set_param(h3, 'Name', 'V');

    % H信号：DC-DC Converter到Motor
    try
        delete_line(model_name, 'DC-DC Converter/1', 'Motor/1');
    catch
        % 连接可能不存在，继续
    end
    h4 = add_line(model_name, 'DC-DC Converter/1', 'Motor/1', 'autorouting', 'on');
    set_param(h4, 'Name', 'H');

    % R信号：Motor_TF到Power_Sum
    try
        delete_line(model_name, 'Motor_TF/1', 'Power_Sum/2');
    catch
        % 连接可能不存在，继续
    end
    h5 = add_line(model_name, 'Motor_TF/1', 'Power_Sum/2', 'autorouting', 'on');
    set_param(h5, 'Name', 'R');
catch ME
    fprintf('警告: 设置信号标签时出现错误: %s\n', ME.message);
end

% Vehicle Body输出信号
try
    h6 = get_param([model_name '/Vehicle Body'], 'LineHandles');
    if ~isempty(h6.Outport) && length(h6.Outport) >= 1 && h6.Outport(1) ~= -1
        set_param(h6.Outport(1), 'Name', 'Vehicle_Speed');
    end
catch ME
    fprintf('警告: 设置Vehicle Body输出信号标签时出现错误: %s\n', ME.message);
end

%% {{ AURA-X: Create - 设置模型配置参数 }}
% 设置仿真参数
try
    set_param(model_name, 'StopTime', '10');
    set_param(model_name, 'SolverType', 'Variable-step');
    set_param(model_name, 'Solver', 'ode45');
catch ME
    fprintf('警告: 设置仿真参数时出现错误: %s\n', ME.message);
end

% 设置模型布局 - 使用try-catch处理可能不存在的参数
try
    set_param(model_name, 'ShowPortLabels', 'on');
catch
    % 参数可能不存在，跳过
end
try
    set_param(model_name, 'ShowLineDimensions', 'on');
catch
    % 参数可能不存在，跳过
end

%% {{ AURA-X: Create - 保存模型 }}
save_system(model_name);

%% {{ AURA-X: Create - 优化模型布局和显示 }}
% 自动布局优化 (如果Matlab版本支持)
try
    Simulink.BlockDiagram.arrangeSystem(model_name);
catch
    % 如果不支持自动布局，跳过此步骤
    fprintf('注意: 当前Matlab版本不支持自动布局功能\n');
end

% 设置模型视图 - 使用try-catch处理可能不存在的参数
try
    set_param(model_name, 'ZoomFactor', '100');
catch
    % 参数可能不存在，跳过
end
try
    set_param(model_name, 'ShowPortLabels', 'FromPortIcon');
catch
    % 参数可能不存在，跳过
end
try
    set_param(model_name, 'ShowSignalDimensions', 'on');
catch
    % 参数可能不存在，跳过
end

% 强制刷新模型显示
try
    refresh_param(model_name);
catch
    % 如果refresh_param不存在，跳过
end

%% 显示完成信息
fprintf('\n=== 混合动力汽车控制系统模型创建完成 ===\n');
fprintf('模型名称: %s\n', model_name);
fprintf('模型文件已保存到当前目录\n');
fprintf('\n==== 系统架构组件 ====\n');
fprintf('动力系统:\n');
fprintf('  ├─ Engine (发动机) - 传递函数: 1/(0.1s+1)\n');
fprintf('  ├─ Battery (电池) - 400V恒定电压源\n');
fprintf('  ├─ DC-DC Converter (直流转换器) - 增益: 1.5\n');
fprintf('  ├─ Motor (电机) - 传递函数: 100/(0.05s+1)\n');
fprintf('  ├─ Gearbox (变速箱) - 齿轮比: 3.5\n');
fprintf('  └─ Vehicle Body (车辆本体) - 传递函数: 1/(1000s²+50s+1)\n');
fprintf('\n控制系统:\n');
fprintf('  ├─ Speed Command (速度指令) - 阶跃信号: 0→60\n');
fprintf('  ├─ Strategy Routing (策略路由) - 智能功率分配\n');
fprintf('  └─ Power_Sum (功率求和) - 合并发动机和电机功率\n');
fprintf('\n监测系统:\n');
fprintf('  ├─ Scopes (示波器) - 实时波形显示\n');
fprintf('  ├─ Speed (速度显示) - 当前车速\n');
fprintf('  └─ ref_spd (参考速度) - 目标速度显示\n');
fprintf('\n==== 信号流路径 ====\n');
fprintf('机械路径: Engine --DF--> Gearbox --F--> Power_Sum --> Vehicle Body\n');
fprintf('电力路径: Battery --V--> DC-DC --H--> Motor --R--> Power_Sum\n');
fprintf('控制路径: Speed Command --> Strategy Routing --> Motor/Engine\n');
fprintf('\n模型已准备就绪，包含%d个模块，可以运行仿真！\n', length(find_system(model_name, 'Type', 'block')) - 1);
fprintf('运行命令: sim(''%s'')\n', model_name);

end